<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊图片上传图床工具</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='fonts/bootstrap-icons.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }

        .header {
            background: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 20px;
        }

        .file-drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .progress {
            height: 20px;
            border-radius: 10px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .result-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .result-item.error {
            border-left-color: #dc3545;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            font-weight: 600;
            margin-right: 5px;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 15px 15px 15px;
            padding: 30px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .status-processing {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .history-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
        }

        .table tbody td {
            padding: 15px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .url-input-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }

            .feature-card {
                margin: 10px 0;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-cloud-upload"></i> 亚马逊图片上传图床工具</h1>
                <p class="mb-0">专为亚马逊模板上传设计：图片上传→生成URL映射表→模板填充→完整模板</p>
            </div>

            <!-- 主要内容 -->
            <div class="p-4">
                <!-- 导航标签 -->
                <ul class="nav nav-tabs mb-4" id="mainTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="rename-tab" data-bs-toggle="tab" data-bs-target="#rename-tab-pane" type="button" role="tab" aria-controls="rename-tab-pane" aria-selected="true">
                            <i class="bi bi-pencil-square"></i> 图片重命名
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-tab-pane" type="button" role="tab" aria-controls="upload-tab-pane" aria-selected="false">
                            <i class="bi bi-cloud-upload"></i> 图片上传
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="url-tab" data-bs-toggle="tab" data-bs-target="#url-tab-pane" type="button" role="tab" aria-controls="url-tab-pane" aria-selected="false">
                            <i class="bi bi-link-45deg"></i> URL解析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="template-tab" data-bs-toggle="tab" data-bs-target="#template-tab-pane" type="button" role="tab" aria-controls="template-tab-pane" aria-selected="false">
                            <i class="bi bi-file-earmark-spreadsheet"></i> 模板填充
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-tab-pane" type="button" role="tab" aria-controls="history-tab-pane" aria-selected="false">
                            <i class="bi bi-clock-history"></i> 历史记录
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="mainTabContent">
                    <!-- 图片重命名选项卡 -->
                    <div class="tab-pane fade show active" id="rename-tab-pane" role="tabpanel" aria-labelledby="rename-tab" tabindex="0">
                        <div class="row">
                            <div class="col-12">
                                <div class="feature-card">
                                    <div class="text-center mb-4">
                                        <div class="feature-icon">
                                            <i class="bi bi-pencil-square"></i>
                                        </div>
                                        <h4>图片重命名工具</h4>
                                        <p class="text-muted">使用Excel文件中的SKU、ASIN、Color映射关系重命名图片文件</p>
                                    </div>

                                    <form id="renameForm" enctype="multipart/form-data">
                                        <!-- Excel文件选择 -->
                                        <div class="mb-4">
                                            <label for="rename_excel_file" class="form-label">
                                                <i class="bi bi-file-earmark-excel-fill text-success"></i>
                                                选择Excel文件 <span class="text-danger">*</span>
                                            </label>
                                            <input type="file" class="form-control" id="rename_excel_file" 
                                                   accept=".xlsx,.xls,.xlsm" required>
                                            <div class="form-text">
                                                Excel文件应包含SKU、ASIN列，可选Color列用于颜色匹配
                                            </div>
                                        </div>

                                        <!-- 图片文件夹选择 -->
                                        <div class="mb-4">
                                            <label for="rename_image_folder" class="form-label">
                                                <i class="bi bi-folder2-open text-primary"></i>
                                                选择图片文件夹 <span class="text-danger">*</span>
                                            </label>
                                            <input type="file" class="form-control" id="rename_image_folder" 
                                                   webkitdirectory multiple required>
                                            <div class="form-text">
                                                选择包含需要重命名图片的文件夹，支持嵌套目录结构
                                            </div>
                                        </div>

                                        <!-- 处理选项 -->
                                        <div class="mb-4">
                                            <label class="form-label">
                                                <i class="bi bi-gear-fill text-warning"></i>
                                                处理选项 <span class="text-danger">*</span>
                                            </label>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               name="process_main" id="process_main" checked>
                                                        <label class="form-check-label" for="process_main">
                                                            <i class="bi bi-image"></i> 处理主图 (白底图)
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               name="process_scene" id="process_scene" checked>
                                                        <label class="form-check-label" for="process_scene">
                                                            <i class="bi bi-images"></i> 处理场景图
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               name="process_swatch" id="process_swatch">
                                                        <label class="form-check-label" for="process_swatch">
                                                            <i class="bi bi-palette"></i> 处理色块图
                                                        </label>
                                                        <div class="form-text">
                                                            <small class="text-info">
                                                                <i class="bi bi-lightbulb"></i>
                                                                需要Excel包含Color列和图片文件夹名包含<strong>【色块】</strong>字样
                                                            </small>
                                                            <small class="text-muted d-block mt-1">
                                                                <i class="bi bi-folder"></i>
                                                                支持：色块、色块-317、swatch色块 等命名
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               name="scene_generic" id="scene_generic">
                                                        <label class="form-check-label" for="scene_generic">
                                                            <i class="bi bi-diagram-3"></i> 使用通用类型场景图处理
                                                        </label>
                                                        <div class="form-text">
                                                            <small>使用图片Width分类：根据宽度范围分类图片</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-primary" id="renameSubmitBtn" onclick="handleRenameSubmit(event); console.log('按钮被点击了！');">
                                                        <i class="bi bi-pencil-square me-2"></i>开始处理
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" id="testColorMatchBtn" onclick="handleColorMatchTest(event);" 
                                                            data-bs-toggle="tooltip" data-bs-placement="top" 
                                                            title="在处理色块图前预检测Excel颜色与图片文件的匹配关系">
                                                        <i class="bi bi-palette me-2"></i>测试颜色匹配
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- 使用说明 -->
                                                <div class="alert alert-info">
                                                    <h6><i class="bi bi-info-circle"></i> 使用说明</h6>
                                                    <ol class="mb-2 small">
                                                        <li>选择包含SKU、ASIN映射关系的Excel文件</li>
                                                        <li>选择需要重命名的图片文件夹</li>
                                                        <li>根据需要选择处理选项</li>
                                                        <li>点击"开始处理"进行批量重命名</li>
                                                        <li>下载包含重命名后图片的ZIP文件</li>
                                                    </ol>
                                                    <div class="alert alert-warning py-2 mb-0">
                                                        <small>
                                                            <i class="bi bi-exclamation-triangle"></i>
                                                            <strong>重要提示：</strong>处理色块图时，请确保图片文件夹中包含名称含有<strong>【色块】</strong>字样的子目录
                                                            <br>
                                                            <i class="bi bi-folder"></i>
                                                            例如：色块、色块-317、swatch色块、色块图片 等
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 测试颜色匹配功能详细说明 -->
                                        <div class="row mt-4">
                                            <div class="col-12">
                                                <div class="alert alert-light border">
                                                    <h6><i class="bi bi-palette text-primary"></i> 关于"测试颜色匹配"功能</h6>
                                                    
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6 class="text-primary"><i class="bi bi-question-circle"></i> 功能用途</h6>
                                                            <p class="small mb-2">
                                                                这是一个<strong>预检测工具</strong>，帮助您在正式重命名前验证Excel文件中的颜色信息与色块图片文件的匹配关系。
                                                            </p>
                                                            <p class="small mb-2">
                                                                <i class="bi bi-check-circle text-success"></i> 避免重命名失败<br>
                                                                <i class="bi bi-check-circle text-success"></i> 提前发现问题<br>
                                                                <i class="bi bi-check-circle text-success"></i> 节省处理时间
                                                            </p>
                                                        </div>
                                                        
                                                        <div class="col-md-6">
                                                            <h6 class="text-primary"><i class="bi bi-gear"></i> 适用场景</h6>
                                                            <p class="small mb-2">
                                                                当您要处理<strong>色块图</strong>（勾选"处理色块图"选项）时使用：
                                                            </p>
                                                            <ul class="small mb-2">
                                                                <li>Excel文件包含<strong>Color列</strong></li>
                                                                <li>图片文件夹包含名称含<strong>【色块】</strong>字样的子目录</li>
                                                                <li>色块图片文件名与颜色名称对应</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="row mt-3">
                                                        <div class="col-md-6">
                                                            <h6 class="text-primary"><i class="bi bi-list-check"></i> 测试内容</h6>
                                                            <ul class="small mb-0">
                                                                <li>检查Excel中的颜色名称</li>
                                                                <li>扫描【色块】文件夹中的图片文件</li>
                                                                <li>智能匹配颜色名称（忽略大小写、空格等）</li>
                                                                <li>生成匹配成功率报告</li>
                                                                <li>列出未匹配的问题并提供解决建议</li>
                                                            </ul>
                                                        </div>
                                                        
                                                        <div class="col-md-6">
                                                            <h6 class="text-primary"><i class="bi bi-lightbulb"></i> 使用建议</h6>
                                                            <ul class="small mb-0">
                                                                <li><strong>建议</strong>：处理色块图前先测试</li>
                                                                <li><strong>目标</strong>：匹配率达到90%以上</li>
                                                                <li><strong>提示</strong>：支持多种颜色名称格式</li>
                                                                <li><strong>优势</strong>：避免重复上传和处理</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mt-3 p-2 bg-light rounded">
                                                        <small class="text-muted">
                                                            <i class="bi bi-info-circle"></i>
                                                            <strong>示例：</strong>如果Excel中有颜色"Deep Red"，色块文件夹中有"deep_red.jpg"或"DEEP RED.jpg"等文件，系统会自动识别并匹配。
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 错误提示区域 -->
                                        <div id="rename-error" class="alert alert-danger mt-3" style="display: none;"></div>
                                    </form>

                                    <!-- 测试和处理结果显示区域 -->
                                    <div id="rename-results-section" class="mt-4" style="display: none;">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="bi bi-clipboard-data text-primary"></i> 
                                                    颜色匹配测试结果
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="color-match-results">
                                                    <!-- 结果内容将通过JavaScript填充 -->
                                                </div>
                                                <div class="mt-3">
                                                    <small class="text-muted">
                                                        <i class="bi bi-info-circle"></i>
                                                        <strong>使用提示：</strong>如果匹配率达到90%以上，可以继续进行重命名处理。如果匹配率较低，请根据上方建议调整文件命名后重新测试。
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图片上传选项卡 -->
                    <div class="tab-pane fade" id="upload-tab-pane" role="tabpanel" aria-labelledby="upload-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-images"></i>
                                    </div>
                                    <h4 class="text-center mb-4">批量上传图片到图床</h4>
                                    <div class="alert alert-success">
                                        <h6><i class="bi bi-target"></i> 核心目的</h6>
                                        <p class="mb-2"><strong>为亚马逊模板填充生成图片URL映射关系表</strong></p>
                                        <p class="mb-0 small">上传图片到图床获得外链 → 生成包含ASIN/MSKU与图片URL对应关系的映射表 → 用于模板填充功能</p>
                                    </div>
                                    <div class="alert alert-primary">
                                        <h6><i class="bi bi-list-ol"></i> 上传流程说明</h6>
                                        <ol class="mb-0">
                                            <li><strong>选择图片文件或文件夹</strong> - 支持拖拽上传</li>
                                            <li><strong>设置上传目录</strong> - 指定图床存储路径</li>
                                            <li><strong>选择任务列表</strong> - 可选，用于自动填充商品信息</li>
                                            <li><strong>开始上传</strong> - 批量上传并生成URL映射表</li>
                                        </ol>
                                    </div>
                                    <div class="alert alert-info">
                                        <small><i class="bi bi-info-circle"></i>
                                            <strong>图片任务列表说明:</strong> 可选择包含ASIN、SKU、MSKU信息的Excel文件，用于自动填充映射表中的商品信息。
                                        </small>
                                    </div>

                                    <!-- 文件拖拽区域 -->
                                    <div class="file-drop-zone" id="fileDropZone">
                                        <i class="bi bi-folder2-open"
                                            style="font-size: 48px; color: #667eea; margin-bottom: 20px;"></i>
                                        <h5><i class="bi bi-1-circle-fill text-primary"></i> 第一步：拖拽图片文件夹到此处或点击选择图片</h5>
                                        <p class="text-muted">支持 PNG, JPG, JPEG, GIF, BMP, WEBP 格式</p>
                                        <p class="text-muted">文件名格式: ASIN_类型.扩展名 (如: B07XXXXX_MAIN.jpg)</p>
                                        <p class="text-warning"><small><i class="bi bi-exclamation-triangle"></i>
                                                建议选择包含所有图片的文件夹，与桌面版本操作一致</small></p>
                                        <input type="file" id="fileInput" multiple accept="image/*" webkitdirectory
                                            style="display: none;">
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                id="selectFilesBtn">
                                                <i class="bi bi-images"></i> 选择图片文件
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="selectFolderBtn">
                                                <i class="bi bi-folder2-open"></i> 选择图片文件夹 (推荐)
                                            </button>
                                        </div>
                                        <input type="file" id="filesInput" multiple accept="image/*"
                                            style="display: none;">
                                    </div>

                                    <!-- 上传设置 -->
                                    <div class="mt-4" id="uploadSettings" style="display: none;">
                                        <div class="alert alert-warning">
                                            <h6><i class="bi bi-folder-plus"></i> 第二步：设置上传目录</h6>
                                            <p class="mb-3">
                                                <strong>请输入要在图床创建的一级目录名</strong><br>
                                                <small class="text-muted">与桌面版本操作完全一致，这是必须的步骤</small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="text" class="form-control" id="uploadFolder"
                                                        placeholder="请输入目录名（不能包含/或\，不能为空）">
                                                    <small class="text-muted">
                                                        <i class="bi bi-lightbulb"></i>
                                                        示例：产品图片、新品上传、DEFAULT、测试图片 等
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-success w-100" id="confirmFolderBtn"
                                                        disabled>
                                                        <i class="bi bi-arrow-right"></i> 下一步
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 任务列表文件选择 -->
                                    <div class="mt-4" id="taskListSection" style="display: none;">
                                        <div class="alert alert-info">
                                            <h6><i class="bi bi-file-earmark-excel"></i> 第三步：选择图片任务列表 (可选)</h6>
                                            <p class="mb-3">
                                                <strong>选择包含ASIN、SKU、MSKU信息的Excel文件</strong><br>
                                                <small class="text-muted">
                                                    用于自动填充映射表中的商品信息，与桌面版本功能完全一致
                                                </small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="file" class="form-control" id="taskListFile"
                                                        accept=".xlsx,.xls,.xlsm">
                                                    <small class="text-muted mt-1 d-block">
                                                        <i class="bi bi-info-circle"></i>
                                                        Excel文件应包含ASIN、SKU、MSKU等列
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-primary w-100" id="startUploadBtn">
                                                        <i class="bi bi-cloud-upload"></i> 开始上传
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="alert alert-light mt-3 mb-0">
                                                <small class="text-muted">
                                                    <i class="bi bi-lightbulb"></i>
                                                    <strong>提示：</strong>如果不需要任务列表，可直接点击"开始上传"按钮
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 选中的文件列表 -->
                                    <div id="selectedFiles" class="mt-4" style="display: none;">
                                        <h6>已选择的文件:</h6>
                                        <div id="fileList" class="list-group"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 上传进度 -->
                                <div class="feature-card" id="progressCard" style="display: none;">
                                    <h5><i class="bi bi-activity"></i> 上传进度</h5>
                                    <div class="progress mb-3">
                                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">
                                        </div>
                                    </div>
                                    <div id="progressText">准备上传...</div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            成功: <span id="successCount">0</span> |
                                            失败: <span id="failCount">0</span> |
                                            总计: <span id="totalCount">0</span>
                                        </small>
                                    </div>
                                </div>

                                <!-- 上传结果 -->
                                <div class="feature-card" id="resultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 上传完成</h5>
                                    <div id="uploadResults"></div>
                                    <div class="mt-3" id="downloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadExcel">
                                            <i class="bi bi-download"></i> 下载映射表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- URL解析选项卡 -->
                    <div class="tab-pane fade" id="url-tab-pane" role="tabpanel" aria-labelledby="url-tab" tabindex="0">
                        <div class="url-input-area">
                            <h5 class="mb-3">解析已有URL生成映射表</h5>
                            <div class="alert alert-success">
                                <h6><i class="bi bi-target"></i> 核心目的</h6>
                                <p class="mb-0"><strong>将已有的图片URL解析成标准映射表格式，用于模板填充功能</strong></p>
                            </div>
                            <p class="text-muted">每行粘贴一个图片URL，系统会自动提取ASIN和图片类型，生成与图片上传功能相同格式的映射表。</p>
                            <textarea id="urlInput" class="form-control" rows="8" placeholder="https://example.com/image/B07XXXXX_MAIN.jpg
https://example.com/image/B07XXXXX_PT01.jpg"></textarea>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button class="btn btn-primary w-100" id="parseUrlBtn">
                                        <i class="bi bi-file-earmark-spreadsheet"></i> 解析URL数据
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-secondary w-100" id="clearUrlBtn">
                                        <i class="bi bi-trash"></i> 清空
                                    </button>
                                </div>
                            </div>
                            
                            <!-- URL解析结果和任务列表选择 -->
                            <div id="urlParseSteps" style="display: none;">
                                <!-- 第一步：URL解析结果预览 -->
                                <div class="alert alert-success mt-3" id="urlParseSuccess">
                                    <h6><i class="bi bi-check-circle"></i> URL解析完成</h6>
                                    <p class="mb-0">
                                        成功解析 <span id="parsedUrlCount">0</span> 个URL，涉及 <span id="parsedAsinCount">0</span> 个ASIN
                                    </p>
                                </div>
                                
                                <!-- 第二步：任务列表文件选择（可选） -->
                                <div class="alert alert-info mt-3">
                                    <h6><i class="bi bi-file-earmark-excel"></i> 第二步：选择图片任务列表文件（可选）</h6>
                                    <p class="mb-3">
                                        <strong>选择包含ASIN、SKU、MSKU信息的Excel文件</strong><br>
                                        <small class="text-muted">
                                            用于自动填充映射表中的商品信息，与桌面版本功能完全一致
                                        </small>
                                    </p>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="file" class="form-control" id="urlTaskListFile" accept=".xlsx,.xls,.xlsm">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-success w-100" id="generateUrlExcelBtn">
                                                <i class="bi bi-file-earmark-spreadsheet"></i> 生成Excel映射表
                                            </button>
                                        </div>
                                    </div>
                                    <div class="form-text mt-2">
                                        如果不选择任务列表文件，将生成包含基本URL映射的Excel表格
                                    </div>
                                </div>
                            </div>
                            
                            <!-- URL映射结果显示 -->
                            <div id="urlResultCard" class="feature-card mt-3" style="display: none;">
                                <h5><i class="bi bi-check-circle"></i> 映射表生成完成</h5>
                                <div id="urlResultSummary">
                                    <p class="mb-2">
                                        <i class="bi bi-bar-chart"></i> 统计信息：
                                        共处理 <span id="urlAsinCount">0</span> 个ASIN，
                                        <span id="urlTotalCount">0</span> 个URL链接
                                    </p>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-success w-100" id="downloadUrlExcel">
                                        <i class="bi bi-download"></i> 下载映射表
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模板填充选项卡 -->
                    <div class="tab-pane fade" id="template-tab-pane" role="tabpanel" aria-labelledby="template-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-file-earmark-excel"></i>
                                    </div>
                                    <h4 class="text-center mb-4">填充亚马逊模板</h4>
                                    <div class="alert alert-success">
                                        <h6><i class="bi bi-target"></i> 核心目的</h6>
                                        <p class="mb-2"><strong>使用图片映射表自动填充亚马逊模板中的图片URL字段</strong></p>
                                        <p class="mb-0 small">基于图片上传功能生成的映射表，自动填充main_image_url、other_image_url1-8等字段</p>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-diagram-3"></i> 数据关联逻辑</h6>
                                        <p class="mb-1 small"><strong>主要关联：</strong>图片映射表.MSKU = 商品分类报告.SKU = 亚马逊模版.item_sku</p>
                                        <p class="mb-0 small"><strong>辅助关联：</strong>图片映射表.SKU = 产品资料表.*SKU（必填）</p>
                                    </div>

                                    <div class="alert alert-primary">
                                        <h6><i class="bi bi-list-ol"></i> 填充流程说明</h6>
                                        <ol class="mb-0">
                                            <li><strong>选择亚马逊模板文件</strong> - 要填充的Excel模板</li>
                                            <li><strong>选择商品分类报告</strong> - 包含商品数据的报告文件</li>
                                            <li><strong>选择图片映射文件</strong> - 必选，来源于图片上传功能生成的映射表</li>
                                            <li><strong>选择产品信息文件</strong> - 可选，包含包装尺寸等信息</li>
                                            <li><strong>设置市场和选项</strong> - 选择目标市场和处理选项</li>
                                            <li><strong>开始填充</strong> - 自动处理并生成完整的可上传模板</li>
                                        </ol>
                                    </div>

                                    <!-- 必需文件上传区域 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="templateFile" class="form-label">亚马逊模板文件 <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="templateFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择要填充的亚马逊模板Excel文件</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="reportFile" class="form-label">商品分类报告 <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="reportFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择包含商品数据的分类报告文件</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 必需文件上传区域第二行 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="mappingFile" class="form-label">图片映射文件 <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="mappingFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">来源于图片上传功能生成的映射表，包含ASIN/MSKU与图片URL对应关系</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="productInfoFile" class="form-label">产品信息文件 (可选)</label>
                                                <input type="file" class="form-control" id="productInfoFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">包含包装尺寸、重量等信息的文件</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 设置选项 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="marketSelect" class="form-label">目标市场</label>
                                                <select class="form-select" id="marketSelect">
                                                    <option value="US">美国 (US)</option>
                                                    <option value="UK">英国 (UK)</option>
                                                </select>
                                                <div class="form-text">选择目标销售市场，影响默认值设置</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check mt-4">
                                                    <input class="form-check-input" type="checkbox" id="useProductInfo"
                                                        checked>
                                                    <label class="form-check-label" for="useProductInfo">
                                                        使用产品信息文件数据
                                                    </label>
                                                    <div class="form-text">启用后将使用产品信息文件中的包装数据</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button class="btn btn-primary" id="fillTemplateBtn" disabled>
                                            <i class="bi bi-gear"></i> 开始填充模板
                                        </button>
                                        <button class="btn btn-outline-secondary" id="clearTemplateBtn">
                                            <i class="bi bi-trash"></i> 清空选择
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试功能 -->
                                <div class="feature-card mt-4">
                                    <h5><i class="bi bi-file-text"></i> 测试报告文件</h5>
                                    <p class="text-muted">上传商品报告文件，测试数据结构和内容</p>

                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="file" class="form-control" id="testReportFile"
                                                accept=".xlsx,.xls,.xlsm">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-outline-primary w-100" id="testReportBtn" disabled>
                                                <i class="bi bi-search"></i> 测试报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 模板填充结果 -->
                                <div class="feature-card" id="templateResultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 处理结果</h5>
                                    <div id="templateResults"></div>
                                    <div class="mt-3" id="templateDownloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadTemplateResult">
                                            <i class="bi bi-download"></i> 下载结果
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试结果 -->
                                <div class="feature-card" id="reportTestCard" style="display: none;">
                                    <h5><i class="bi bi-clipboard-data"></i> 报告分析</h5>
                                    <div id="reportTestResults"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录选项卡 -->
                    <div class="tab-pane fade" id="history-tab-pane" role="tabpanel" aria-labelledby="history-tab" tabindex="0">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-clock-history"></i> 历史映射表</h4>
                            <button class="btn btn-outline-primary" id="refreshHistory">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>

                        <div class="history-table">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>文件大小</th>
                                        <th>修改时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-3">处理中，请稍候...</div>
        </div>
    </div>

    <!-- jQuery库 -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}" 
            onerror="console.error('❌ 本地jQuery加载失败，尝试CDN版本'); this.src='https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js'"></script>
    
    <!-- Bootstrap JS with CDN fallback -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}" 
            onerror="console.error('❌ 本地Bootstrap加载失败，尝试CDN版本'); loadBootstrapFromCDN()"></script>

    <!-- Bootstrap CDN备用方案 -->
    <script>
        /**
         * 🛡️ Bootstrap CDN备用加载函数
         * 当本地Bootstrap文件加载失败时自动使用CDN版本
         */
        function loadBootstrapFromCDN() {
            console.log('🔄 正在从CDN加载Bootstrap...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
            script.onload = function() {
                console.log('✅ Bootstrap CDN版本加载成功');
                // 重新初始化Bootstrap相关功能
                initializeBootstrapFeatures();
            };
            script.onerror = function() {
                console.error('❌ Bootstrap CDN版本也加载失败，使用手动实现');
                window.bootstrapFallbackMode = true;
                initializeManualBootstrapFeatures();
            };
            document.head.appendChild(script);
        }

        /**
         * 🔧 初始化Bootstrap功能
         */
        function initializeBootstrapFeatures() {
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap 全局对象可用');
                
                // 初始化Bootstrap工具提示
                try {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                    console.log(`✅ 初始化了 ${tooltipList.length} 个工具提示`);
                } catch (error) {
                    console.error('⚠️ 工具提示初始化失败:', error);
                }
                
                // 初始化Bootstrap Tab功能
                initializeBootstrapTabs();
                
                // 初始化Bootstrap模态框
                initializeBootstrapModals();
                
                return true;
            }
            return false;
        }

        /**
         * 📋 初始化Bootstrap Tab功能
         */
        function initializeBootstrapTabs() {
            try {
                const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
                console.log(`🔍 找到 ${tabButtons.length} 个Tab按钮`);
                
                tabButtons.forEach((button, index) => {
                    try {
                        const tabInstance = new bootstrap.Tab(button);
                        console.log(`✅ Tab按钮 ${index + 1} 初始化成功:`, button.getAttribute('data-bs-target'));
                        
                        // 添加点击事件监听
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            console.log('[Tab调试] 点击Tab按钮:', this.getAttribute('data-bs-target'));
                            tabInstance.show();
                        });
                        
                    } catch (error) {
                        console.error(`❌ Tab按钮 ${index + 1} 初始化失败:`, error);
                        // 为失败的Tab添加手动处理
                        addManualTabHandler(button);
                    }
                });
                
            } catch (error) {
                console.error('❌ Bootstrap Tab整体初始化失败:', error);
                initializeManualTabs();
            }
        }

        /**
         * 📦 初始化Bootstrap模态框
         */
        function initializeBootstrapModals() {
            try {
                const modalTriggers = document.querySelectorAll('[data-bs-toggle="modal"]');
                console.log(`🔍 找到 ${modalTriggers.length} 个模态框触发器`);
                
                modalTriggers.forEach((trigger, index) => {
                    try {
                        trigger.addEventListener('click', function(e) {
                            e.preventDefault();
                            const targetId = this.getAttribute('data-bs-target');
                            const targetModal = document.querySelector(targetId);
                            if (targetModal && typeof bootstrap !== 'undefined') {
                                const modalInstance = new bootstrap.Modal(targetModal);
                                modalInstance.show();
                            }
                        });
                        
                    } catch (error) {
                        console.error(`❌ 模态框触发器 ${index + 1} 初始化失败:`, error);
                    }
                });
                
            } catch (error) {
                console.error('❌ Bootstrap模态框整体初始化失败:', error);
            }
        }

        /**
         * 🛠️ 手动实现Bootstrap功能（备用方案）
         */
        function initializeManualBootstrapFeatures() {
            console.log('🔧 启用手动Bootstrap功能实现');
            window.bootstrapFallbackMode = true;
            
            // 手动实现Tab功能
            initializeManualTabs();
            
            // 手动实现模态框功能
            initializeManualModals();
            
            // 手动实现下拉菜单功能
            initializeManualDropdowns();
        }

        /**
         * 📋 手动实现Tab切换功能
         */
        function initializeManualTabs() {
            console.log('🔧 启用手动Tab切换功能');
            
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                addManualTabHandler(button);
            });
        }

        /**
         * 🎯 为单个Tab按钮添加手动处理
         */
        function addManualTabHandler(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('[手动Tab] 点击Tab按钮:', this.getAttribute('data-bs-target'));
                
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);
                
                if (targetPane) {
                    // 隐藏所有tab内容
                    const allPanes = document.querySelectorAll('.tab-pane');
                    allPanes.forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });
                    
                    // 移除所有tab按钮的active状态
                    const allButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
                    allButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    
                    // 显示目标tab内容
                    targetPane.classList.add('show', 'active');
                    
                    // 激活当前tab按钮
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                    
                    console.log('✅ 手动Tab切换完成');
                    
                    // 如果有amazonUploader实例，调用其Tab切换方法
                    if (window.amazonUploader && window.amazonUploader.manualTabSwitch) {
                        window.amazonUploader.manualTabSwitch(targetId);
                    }
                } else {
                    console.error('❌ 未找到目标Tab内容:', targetId);
                }
            });
        }

        /**
         * 📦 手动实现模态框功能
         */
        function initializeManualModals() {
            console.log('🔧 启用手动模态框功能');
            
            const modalTriggers = document.querySelectorAll('[data-bs-toggle="modal"]');
            modalTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('data-bs-target');
                    showManualModal(targetId);
                });
            });
            
            // 为关闭按钮添加事件
            document.addEventListener('click', function(e) {
                if (e.target.hasAttribute('data-bs-dismiss') && e.target.getAttribute('data-bs-dismiss') === 'modal') {
                    const modal = e.target.closest('.modal');
                    if (modal) {
                        hideManualModal(modal);
                    }
                }
            });
        }

        /**
         * 📦 显示手动模态框
         */
        function showManualModal(targetId) {
            const modal = document.querySelector(targetId);
            if (modal) {
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');
                
                // 添加背景遮罩
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.onclick = () => hideManualModal(modal);
                document.body.appendChild(backdrop);
                
                console.log('✅ 手动模态框显示成功');
            }
        }

        /**
         * 📦 隐藏手动模态框
         */
        function hideManualModal(modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
            
            // 移除背景遮罩
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            console.log('✅ 手动模态框隐藏成功');
        }

        /**
         * 📂 手动实现下拉菜单功能
         */
        function initializeManualDropdowns() {
            console.log('🔧 启用手动下拉菜单功能');
            
            const dropdownTriggers = document.querySelectorAll('[data-bs-toggle="dropdown"]');
            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const dropdown = this.nextElementSibling;
                    if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                        const isShown = dropdown.classList.contains('show');
                        
                        // 关闭所有其他下拉菜单
                        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                            menu.classList.remove('show');
                        });
                        
                        // 切换当前下拉菜单
                        if (!isShown) {
                            dropdown.classList.add('show');
                        }
                    }
                });
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            });
        }

        /**
         * 🎯 改进的错误处理模态框
         */
        function showDetailedError(title, message) {
            console.log('🚨 显示详细错误信息:', title);
            
            // 移除已存在的错误弹窗
            const existingModal = document.getElementById('errorModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建模态对话框显示详细错误信息
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'errorModal';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle-fill"></i> ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" onclick="hideManualModal(document.getElementById('errorModal'))"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <pre style="white-space: pre-wrap; margin-bottom: 0; font-family: system-ui, -apple-system, sans-serif; font-size: 14px;">${message}</pre>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="hideManualModal(document.getElementById('errorModal'))">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面并显示
            document.body.appendChild(modal);
            
            // 根据Bootstrap是否可用选择显示方式
            if (typeof bootstrap !== 'undefined' && !window.bootstrapFallbackMode) {
                try {
                    const modalInstance = new bootstrap.Modal(modal);
                    modalInstance.show();
                    
                    // 监听关闭事件，清理DOM元素
                    modal.addEventListener('hidden.bs.modal', function () {
                        modal.remove();
                    });
                } catch (error) {
                    console.error('❌ Bootstrap模态框创建失败，使用手动方式:', error);
                    showManualModal('#errorModal');
                }
            } else {
                showManualModal('#errorModal');
            }
        }
    </script>

    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}?v=20250712142000"></script>
    <script>
        // 改进的初始化函数
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 [HTML调试] DOMContentLoaded 事件触发');

            // 等待所有脚本加载完成后再进行初始化
            setTimeout(function() {
                console.log('🔍 开始Bootstrap兼容性检查...');
                
                // 检查Bootstrap是否可用
                if (typeof bootstrap !== 'undefined') {
                    console.log('✅ Bootstrap 全局对象可用');
                    initializeBootstrapFeatures();
                } else {
                    console.warn('⚠️ Bootstrap 全局对象不可用，启用备用方案');
                    initializeManualBootstrapFeatures();
                }

                // 确保只初始化一次应用实例
                if (!window.amazonUploader) {
                    console.log('🔧 [HTML调试] 首次初始化 AmazonImageUploader');
                    window.amazonUploader = new AmazonImageUploader();
                } else {
                    console.log('✅ [HTML调试] AmazonImageUploader 已存在，跳过初始化');
                }

                // 加载配置信息
                loadConfig();
                
                console.log('🎉 页面初始化完成');
            }, 100); // 延迟100ms确保所有脚本都已加载
        });

        // 🛠️ 工具函数库 - 兼容Bootstrap和手动实现
        
        /**
         * 📊 格式化文件大小显示
         */
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 🔄 智能加载状态显示
         */
        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                if (show) {
                    loadingOverlay.style.display = 'flex';
                } else {
                    loadingOverlay.style.display = 'none';
                }
            } else if (show) {
                // 创建加载遮罩
                const overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-size: 18px;
                `;
                overlay.innerHTML = '<div>🔄 处理中，请稍候...</div>';
                document.body.appendChild(overlay);
            }
        }

        /**
         * ❌ 简单错误提示
         */
        function showError(message) {
            console.error('💥 错误:', message);
            
            // 尝试使用Toast通知，如果失败则使用alert
            try {
                showToast('错误', message, 'danger');
            } catch (error) {
                alert('错误: ' + message);
            }
        }

        /**
         * ✅ 简单成功提示
         */
        function showSuccess(message) {
            console.log('🎉 成功:', message);
            
            // 尝试使用Toast通知，如果失败则使用alert
            try {
                showToast('成功', message, 'success');
            } catch (error) {
                alert('成功: ' + message);
            }
        }

        /**
         * 🍞 智能Toast通知 - 兼容Bootstrap和手动实现
         */
        function showToast(title, message, type = 'success') {
            console.log(`📢 显示Toast通知: ${title} - ${message}`);
            
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1055';
                document.body.appendChild(toastContainer);
            }
            
            // 创建toast元素
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <strong>${title}</strong><br>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
                    </div>
                </div>
            `;
            
            const toastElement = document.createElement('div');
            toastElement.innerHTML = toastHtml;
            const toast = toastElement.firstElementChild;
            toastContainer.appendChild(toast);
            
            // 根据Bootstrap是否可用选择显示方式
            if (typeof bootstrap !== 'undefined' && !window.bootstrapFallbackMode) {
                try {
                    const toastInstance = new bootstrap.Toast(toast, {
                        autohide: true,
                        delay: 5000
                    });
                    toastInstance.show();
                    
                    // 监听隐藏事件，清理DOM
                    toast.addEventListener('hidden.bs.toast', function () {
                        toast.remove();
                    });
                } catch (error) {
                    console.error('❌ Bootstrap Toast创建失败，使用手动方式:', error);
                    showManualToast(toast);
                }
            } else {
                showManualToast(toast);
            }
        }

        /**
         * 🍞 手动Toast显示
         */
        function showManualToast(toast) {
            // 显示toast
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            toast.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // 重命名提交处理函数 - 完整的原生JavaScript实现
        function handleRenameSubmit(event) {
            console.log('🔧 重命名提交处理开始');
            event.preventDefault();
            event.stopPropagation();
            
            // 验证表单
            const excelFile = document.getElementById('rename_excel_file').files[0];
            const imageFiles = document.getElementById('rename_image_folder').files;
            const mainChecked = document.getElementById('process_main').checked;
            const sceneChecked = document.getElementById('process_scene').checked;
            const swatchChecked = document.getElementById('process_swatch').checked;
            
            // 验证文件
            if (!excelFile) {
                document.getElementById('rename_excel_file').classList.add('is-invalid');
                return false;
            }
            
            if (imageFiles.length === 0) {
                document.getElementById('rename_image_folder').classList.add('is-invalid');
                return false;
            }
            
            // 验证至少选择了一个处理选项
            if (!mainChecked && !sceneChecked && !swatchChecked) {
                const errorDiv = document.getElementById('rename-error');
                if (errorDiv) {
                    errorDiv.textContent = '请至少选择一个处理选项';
                    errorDiv.style.display = 'block';
                } else {
                    alert('请至少选择一个处理选项');
                }
                return false;
            }
            
            console.log('✅ 表单验证通过，开始提交');
            
            // 显示加载状态
            showLoading(true);
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('excel_file', excelFile);
            
                         // 添加所有图片文件
             for (let i = 0; i < imageFiles.length; i++) {
                 formData.append('image_folder', imageFiles[i], imageFiles[i].webkitRelativePath || imageFiles[i].name);
             }
            
            // 添加处理选项
            if (mainChecked) formData.append('process_main', 'on');
            if (sceneChecked) formData.append('process_scene', 'on');
            if (swatchChecked) formData.append('process_swatch', 'on');
            if (document.getElementById('scene_generic').checked) {
                formData.append('scene_generic', 'on');
            }
            
            console.log('📤 发送重命名请求到服务器');
            
            // 发送请求
            fetch('/api/rename-images', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('📥 收到服务器响应，状态:', response.status);
                
                if (response.ok) {
                    // 成功响应，处理文件下载
                    return response.blob();
                } else {
                    // 错误响应，解析错误信息
                    return response.text().then(text => {
                        try {
                            const errorData = JSON.parse(text);
                            throw new Error(errorData.error || '处理失败');
                        } catch (parseError) {
                            // 如果JSON解析失败，直接使用服务器返回的文本作为错误信息
                            console.log('JSON解析失败，使用原始响应文本:', text);
                            throw new Error(text || '处理失败，请检查文件和选项');
                        }
                    });
                }
            })
            .then(blob => {
                console.log('✅ 文件处理成功，开始下载');
                showLoading(false);
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'renamed_images_' + new Date().getTime() + '.zip';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 显示成功提示
                alert('图片重命名处理完成，已下载ZIP文件！');
            })
            .catch(error => {
                console.error('❌ 处理失败:', error);
                showLoading(false);
                
                // 显示错误信息
                const errorDiv = document.getElementById('rename-error');
                if (errorDiv) {
                    errorDiv.textContent = error.message || '处理失败，请检查文件和选项';
                    errorDiv.style.display = 'block';
                } else {
                    alert(error.message || '处理失败，请检查文件和选项');
                }
             });
         }
         
         // 颜色匹配测试函数
         function handleColorMatchTest(event) {
             console.log('🎨 开始颜色匹配测试');
             event.preventDefault();
             event.stopPropagation();
             
             // 验证表单
             const excelFile = document.getElementById('rename_excel_file').files[0];
             const imageFiles = document.getElementById('rename_image_folder').files;
             
             // 验证文件
             if (!excelFile) {
                 alert('请先选择Excel文件');
                 return false;
             }
             
             if (imageFiles.length === 0) {
                 alert('请先选择图片文件夹');
                 return false;
             }
             
             console.log('✅ 颜色匹配测试验证通过，开始提交');
             
             // 显示加载状态
             showLoading(true);
             
             // 准备表单数据
             const formData = new FormData();
             formData.append('excel_file', excelFile);
             
             // 添加所有图片文件
             for (let i = 0; i < imageFiles.length; i++) {
                 formData.append('image_folder', imageFiles[i], imageFiles[i].webkitRelativePath || imageFiles[i].name);
             }
             
             console.log('📤 发送颜色匹配测试请求到服务器');
             
             // 发送请求
             fetch('/api/test-color-matching', {
                 method: 'POST',
                 body: formData
             })
             .then(response => {
                 console.log('📥 收到颜色匹配测试响应，状态:', response.status);
                 return response.json();
             })
             .then(data => {
                 console.log('✅ 颜色匹配测试成功', data);
                 showLoading(false);
                 
                 // 显示测试结果
                 alert('颜色匹配测试完成！\n结果：' + JSON.stringify(data, null, 2));
             })
             .catch(error => {
                 console.error('❌ 颜色匹配测试失败:', error);
                 showLoading(false);
                 
                 // 显示错误信息
                 alert('颜色匹配测试失败：' + (error.message || '未知错误'));
             });
         }

        // 加载配置信息
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('apiUrl').textContent = result.config.api_url;
                    document.getElementById('apiKey').textContent = result.config.api_key;
                }
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            // 注释掉重复的模板填充功能设置，由app.js处理
            // setupTemplateFeatures(); 
        });

        // 模板填充功能由 app.js 中的 AmazonImageUploader 类处理
        // 以下代码已移至 app.js，避免重复事件绑定
    </script>
</body>

</html>